import time
import cProfile
import pstats
import io
from contextlib import contextmanager
from functools import wraps
from collections import defaultdict
import threading

class PerformanceProfiler:
    """高级性能分析器，支持多种分析方式"""
    
    def __init__(self):
        self.timings = defaultdict(list)
        self.call_counts = defaultdict(int)
        self.memory_usage = {}
        self._lock = threading.Lock()
    
    @contextmanager
    def timer(self, name, verbose=True):
        """计时上下文管理器"""
        start = time.perf_counter()
        start_process = time.process_time()
        
        try:
            yield
        finally:
            end = time.perf_counter()
            end_process = time.process_time()
            
            wall_time = (end - start) * 1000  # 转换为毫秒
            cpu_time = (end_process - start_process) * 1000
            
            with self._lock:
                self.timings[name].append({
                    'wall_time': wall_time,
                    'cpu_time': cpu_time,
                    'timestamp': time.time()
                })
                self.call_counts[name] += 1
            
            if verbose:
                print(f"{name}: {wall_time:.2f} ms (CPU: {cpu_time:.2f} ms)")
    
    def time_function(self, name=None):
        """函数计时装饰器"""
        def decorator(func):
            func_name = name or f"{func.__module__}.{func.__name__}"
            
            @wraps(func)
            def wrapper(*args, **kwargs):
                with self.timer(func_name):
                    return func(*args, **kwargs)
            return wrapper
        return decorator
    
    def profile_function(self, func, *args, **kwargs):
        """使用cProfile分析函数"""
        pr = cProfile.Profile()
        pr.enable()
        
        result = func(*args, **kwargs)
        
        pr.disable()
        
        # 创建统计报告
        s = io.StringIO()
        ps = pstats.Stats(pr, stream=s)
        ps.sort_stats('cumulative')
        ps.print_stats(20)  # 显示前20个最耗时的函数
        
        print("=== cProfile 分析结果 ===")
        print(s.getvalue())
        
        return result
    
    def get_summary(self):
        """获取性能摘要"""
        print("\n=== 性能分析摘要 ===")
        
        for name, timings in self.timings.items():
            if not timings:
                continue
                
            wall_times = [t['wall_time'] for t in timings]
            cpu_times = [t['cpu_time'] for t in timings]
            
            print(f"\n{name}:")
            print(f"  调用次数: {len(timings)}")
            print(f"  总耗时: {sum(wall_times):.2f} ms")
            print(f"  平均耗时: {sum(wall_times)/len(wall_times):.2f} ms")
            print(f"  最小耗时: {min(wall_times):.2f} ms")
            print(f"  最大耗时: {max(wall_times):.2f} ms")
            print(f"  CPU时间: {sum(cpu_times):.2f} ms")
    
    def reset(self):
        """重置所有统计数据"""
        with self._lock:
            self.timings.clear()
            self.call_counts.clear()
            self.memory_usage.clear()

# 全局分析器实例
profiler = PerformanceProfiler()

# 便捷函数
def timer(name, verbose=True):
    return profiler.timer(name, verbose)

def time_function(name=None):
    return profiler.time_function(name)

def profile_function(func, *args, **kwargs):
    return profiler.profile_function(func, *args, **kwargs)

def get_summary():
    return profiler.get_summary()

def reset_profiler():
    return profiler.reset()

# 内存使用监控
try:
    import psutil
    import os
    
    def get_memory_usage():
        """获取当前进程的内存使用情况"""
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        return {
            'rss': memory_info.rss / 1024 / 1024,  # MB
            'vms': memory_info.vms / 1024 / 1024,  # MB
        }
    
    @contextmanager
    def memory_monitor(name):
        """内存使用监控上下文管理器"""
        start_memory = get_memory_usage()
        yield
        end_memory = get_memory_usage()
        
        memory_diff = end_memory['rss'] - start_memory['rss']
        print(f"{name} 内存变化: {memory_diff:+.2f} MB (当前: {end_memory['rss']:.2f} MB)")

except ImportError:
    def get_memory_usage():
        return {"rss": 0, "vms": 0}
    
    @contextmanager
    def memory_monitor(name):
        print(f"{name}: 内存监控不可用 (需要安装 psutil)")
        yield
