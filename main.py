import time
import cv2
import numpy as np
from contextlib import contextmanager
from functools import wraps

@contextmanager
def timer(description):
    """上下文管理器，用于测量代码块的执行时间"""
    start = time.perf_counter()
    yield
    end = time.perf_counter()
    print(f"{description}: {(end - start) * 1000:.2f} ms")

def time_function(func):
    """装饰器，用于测量函数的执行时间"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.perf_counter()
        result = func(*args, **kwargs)
        end = time.perf_counter()
        print(f"{func.__name__}: {(end - start) * 1000:.2f} ms")
        return result
    return wrapper

def main():
    start_time = time.perf_counter()

    with timer("图像读取"):
        img = cv2.imread("test.bmp")

    with timer("颜色范围掩码创建"):
        lower_rgb = np.array([4, 2, 4])
        upper_rgb = np.array([24, 22, 24])
        mask = cv2.inRange(img, lower_rgb, upper_rgb)

    with timer("形态学操作"):
        kernel = np.ones((5, 5), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.medianBlur(mask, 5)

    with timer("轮廓检测"):
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        result_img = img.copy()

    h, w = img.shape[:2]

    with timer("轮廓处理和环形区域提取"):
        for i, cnt in enumerate(contours):
            contour_start = time.perf_counter()

            if cv2.contourArea(cnt) < 50:
                continue

            with timer(f"  轮廓 {i} - 圆形检测"):
                (x, y), radius = cv2.minEnclosingCircle(cnt)
                area = cv2.contourArea(cnt)
                circle_area = np.pi * (radius ** 2)

            if area / circle_area > 0.7:
                with timer(f"  轮廓 {i} - ROI计算"):
                    center = (int(x), int(y))
                    radius = int(radius)
                    outer_radius = radius + 600

                    x0 = max(center[0] - outer_radius, 0)
                    y0 = max(center[1] - outer_radius, 0)
                    x1 = min(center[0] + outer_radius, w)
                    y1 = min(center[1] + outer_radius, h)

                    roi_width = x1 - x0
                    roi_height = y1 - y0

                with timer(f"  轮廓 {i} - 图像裁剪"):
                    roi = img[y0:y1, x0:x1]

                with timer(f"  轮廓 {i} - 掩码创建"):
                    ring_mask = np.zeros((roi_height, roi_width), dtype=np.uint8)
                    local_center = (center[0] - x0, center[1] - y0)
                    cv2.circle(ring_mask, local_center, outer_radius, 255, thickness=-1)

                with timer(f"  轮廓 {i} - 环形区域提取"):
                    ring_cropped = cv2.bitwise_and(roi, roi, mask=ring_mask)

                with timer(f"  轮廓 {i} - 图像保存"):
                    cv2.imwrite(f"ring_{i}.bmp", ring_cropped)

                contour_end = time.perf_counter()
                print(f"  轮廓 {i} 总耗时: {(contour_end - contour_start) * 1000:.2f} ms")

    with timer("结果图像保存"):
        cv2.imwrite("result.bmp", result_img)

    end_time = time.perf_counter()
    print(f"\n总耗时: {(end_time - start_time) * 1000:.2f} ms ({end_time - start_time:.3f} 秒)")

if __name__ == "__main__":
    main()
