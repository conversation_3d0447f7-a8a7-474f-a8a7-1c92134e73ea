import time
import cv2
import numpy as np

def main():
    start_time = time.time()
    img = cv2.imread("test.bmp")

    lower_rgb = np.array([4, 2, 4])
    upper_rgb = np.array([24, 22, 24])
    mask = cv2.inRange(img, lower_rgb, upper_rgb)

    kernel = np.ones((5, 5), np.uint8)
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    mask = cv2.medianBlur(mask, 5)

    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    result_img = img.copy()

    h, w = img.shape[:2]

    for i, cnt in enumerate(contours):
        if cv2.contourArea(cnt) < 50:
            continue

        (x, y), radius = cv2.minEnclosingCircle(cnt)
        area = cv2.contourArea(cnt)
        circle_area = np.pi * (radius ** 2)
        if area / circle_area > 0.7:
            center = (int(x), int(y))
            radius = int(radius)
            outer_radius = radius + 600

            x0 = max(center[0] - outer_radius, 0)
            y0 = max(center[1] - outer_radius, 0)
            x1 = min(center[0] + outer_radius, w)
            y1 = min(center[1] + outer_radius, h)

            roi_width = x1 - x0
            roi_height = y1 - y0

            roi = img[y0:y1, x0:x1]

            ring_mask = np.zeros((roi_height, roi_width), dtype=np.uint8)

            local_center = (center[0] - x0, center[1] - y0)

            cv2.circle(ring_mask, local_center, outer_radius, 255, thickness=-1)
            # cv2.circle(ring_mask, local_center, radius, 0, thickness=-1)

            ring_cropped = cv2.bitwise_and(roi, roi, mask=ring_mask)

            cv2.imwrite(f"ring_{i}.bmp", ring_cropped)

    end_time = time.time()
    print(f"Time taken: {end_time - start_time} seconds")

    cv2.imwrite("result.bmp", result_img)

if __name__ == "__main__":
    main()
