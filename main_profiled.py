import cv2
import numpy as np
from performance_profiler import timer, time_function, profile_function, get_summary, memory_monitor

@time_function("图像读取")
def load_image(filename):
    return cv2.imread(filename)

@time_function("颜色范围掩码")
def create_color_mask(img, lower_rgb, upper_rgb):
    return cv2.inRange(img, lower_rgb, upper_rgb)

@time_function("形态学操作")
def apply_morphology(mask):
    kernel = np.ones((5, 5), np.uint8)
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    mask = cv2.medianBlur(mask, 5)
    return mask

@time_function("轮廓检测")
def find_contours(mask):
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    return contours

def process_contour(img, cnt, i, h, w):
    """处理单个轮廓"""
    with timer(f"轮廓{i}-面积检查"):
        if cv2.contourArea(cnt) < 50:
            return False

    with timer(f"轮廓{i}-圆形检测"):
        (x, y), radius = cv2.minEnclosingCircle(cnt)
        area = cv2.contourArea(cnt)
        circle_area = np.pi * (radius ** 2)
        
    if area / circle_area <= 0.7:
        return False

    with timer(f"轮廓{i}-ROI计算"):
        center = (int(x), int(y))
        radius = int(radius)
        outer_radius = radius + 600

        x0 = max(center[0] - outer_radius, 0)
        y0 = max(center[1] - outer_radius, 0)
        x1 = min(center[0] + outer_radius, w)
        y1 = min(center[1] + outer_radius, h)

        roi_width = x1 - x0
        roi_height = y1 - y0

    with timer(f"轮廓{i}-图像裁剪"):
        roi = img[y0:y1, x0:x1]

    with timer(f"轮廓{i}-掩码创建"):
        ring_mask = np.zeros((roi_height, roi_width), dtype=np.uint8)
        local_center = (center[0] - x0, center[1] - y0)
        cv2.circle(ring_mask, local_center, outer_radius, 255, thickness=-1)

    with timer(f"轮廓{i}-环形提取"):
        ring_cropped = cv2.bitwise_and(roi, roi, mask=ring_mask)

    with timer(f"轮廓{i}-图像保存"):
        cv2.imwrite(f"ring_{i}.bmp", ring_cropped)
    
    return True

def main():
    print("=== 开始性能分析 ===\n")
    
    with memory_monitor("整个程序"):
        with timer("总执行时间"):
            # 图像读取
            img = load_image("test.bmp")
            
            # 颜色掩码
            lower_rgb = np.array([4, 2, 4])
            upper_rgb = np.array([24, 22, 24])
            mask = create_color_mask(img, lower_rgb, upper_rgb)
            
            # 形态学操作
            mask = apply_morphology(mask)
            
            # 轮廓检测
            contours = find_contours(mask)
            result_img = img.copy()
            
            h, w = img.shape[:2]
            
            # 轮廓处理
            with timer("所有轮廓处理"):
                processed_count = 0
                for i, cnt in enumerate(contours):
                    if process_contour(img, cnt, i, h, w):
                        processed_count += 1
                
                print(f"处理了 {processed_count}/{len(contours)} 个有效轮廓")
            
            # 保存结果
            with timer("结果保存"):
                cv2.imwrite("result.bmp", result_img)
    
    # 显示性能摘要
    get_summary()

def main_with_cprofile():
    """使用cProfile进行详细分析"""
    print("=== 使用 cProfile 进行详细分析 ===")
    profile_function(main)

if __name__ == "__main__":
    # 选择分析方式
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--cprofile":
        main_with_cprofile()
    else:
        main()
