#!/usr/bin/env python3
"""
性能基准测试脚本
用于对比不同实现方式的性能差异
"""

import time
import cv2
import numpy as np
import statistics
from performance_profiler import timer, get_summary, reset_profiler

def benchmark_function(func, iterations=5, *args, **kwargs):
    """对函数进行基准测试"""
    times = []
    
    print(f"\n正在测试 {func.__name__} (运行 {iterations} 次)...")
    
    for i in range(iterations):
        start = time.perf_counter()
        result = func(*args, **kwargs)
        end = time.perf_counter()
        
        execution_time = (end - start) * 1000  # 转换为毫秒
        times.append(execution_time)
        print(f"  第 {i+1} 次: {execution_time:.2f} ms")
    
    # 统计信息
    avg_time = statistics.mean(times)
    min_time = min(times)
    max_time = max(times)
    std_dev = statistics.stdev(times) if len(times) > 1 else 0
    
    print(f"  平均时间: {avg_time:.2f} ms")
    print(f"  最小时间: {min_time:.2f} ms")
    print(f"  最大时间: {max_time:.2f} ms")
    print(f"  标准差: {std_dev:.2f} ms")
    
    return {
        'function': func.__name__,
        'times': times,
        'avg': avg_time,
        'min': min_time,
        'max': max_time,
        'std': std_dev,
        'result': result
    }

def original_implementation():
    """原始实现"""
    img = cv2.imread("test.bmp")
    if img is None:
        return None
        
    lower_rgb = np.array([4, 2, 4])
    upper_rgb = np.array([24, 22, 24])
    mask = cv2.inRange(img, lower_rgb, upper_rgb)

    kernel = np.ones((5, 5), np.uint8)
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    mask = cv2.medianBlur(mask, 5)

    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    h, w = img.shape[:2]
    processed = 0
    
    for i, cnt in enumerate(contours):
        if cv2.contourArea(cnt) < 50:
            continue

        (x, y), radius = cv2.minEnclosingCircle(cnt)
        area = cv2.contourArea(cnt)
        circle_area = np.pi * (radius ** 2)
        
        if area / circle_area > 0.7:
            processed += 1
    
    return processed

def optimized_implementation():
    """优化后的实现（示例）"""
    img = cv2.imread("test.bmp")
    if img is None:
        return None
    
    # 使用更高效的颜色空间转换
    lower_rgb = np.array([4, 2, 4])
    upper_rgb = np.array([24, 22, 24])
    mask = cv2.inRange(img, lower_rgb, upper_rgb)

    # 优化形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    mask = cv2.medianBlur(mask, 5)

    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # 预计算常量
    min_area = 50
    min_circularity = 0.7
    processed = 0
    
    for cnt in contours:
        area = cv2.contourArea(cnt)
        if area < min_area:
            continue

        # 使用更快的圆形度计算
        perimeter = cv2.arcLength(cnt, True)
        if perimeter == 0:
            continue
            
        circularity = 4 * np.pi * area / (perimeter * perimeter)
        
        if circularity > min_circularity:
            processed += 1
    
    return processed

def compare_implementations():
    """对比不同实现的性能"""
    print("=== 性能对比测试 ===")
    
    # 检查测试图像是否存在
    try:
        test_img = cv2.imread("test.bmp")
        if test_img is None:
            print("错误: 找不到 test.bmp 文件")
            return
    except Exception as e:
        print(f"错误: 无法读取测试图像 - {e}")
        return
    
    results = []
    
    # 测试原始实现
    result1 = benchmark_function(original_implementation, iterations=3)
    results.append(result1)
    
    # 测试优化实现
    result2 = benchmark_function(optimized_implementation, iterations=3)
    results.append(result2)
    
    # 性能对比
    print("\n=== 性能对比结果 ===")
    if len(results) >= 2:
        speedup = result1['avg'] / result2['avg']
        print(f"优化后速度提升: {speedup:.2f}x")
        print(f"时间减少: {((result1['avg'] - result2['avg']) / result1['avg'] * 100):.1f}%")

def profile_opencv_operations():
    """分析OpenCV操作的性能"""
    print("\n=== OpenCV 操作性能分析 ===")
    
    img = cv2.imread("test.bmp")
    if img is None:
        print("错误: 找不到测试图像")
        return
    
    reset_profiler()
    
    with timer("图像读取"):
        img = cv2.imread("test.bmp")
    
    with timer("颜色空间转换"):
        lower_rgb = np.array([4, 2, 4])
        upper_rgb = np.array([24, 22, 24])
        mask = cv2.inRange(img, lower_rgb, upper_rgb)
    
    with timer("形态学闭运算"):
        kernel = np.ones((5, 5), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    
    with timer("中值滤波"):
        mask = cv2.medianBlur(mask, 5)
    
    with timer("轮廓检测"):
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    with timer("轮廓分析"):
        for cnt in contours:
            if cv2.contourArea(cnt) < 50:
                continue
            cv2.minEnclosingCircle(cnt)
    
    get_summary()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--compare":
            compare_implementations()
        elif sys.argv[1] == "--opencv":
            profile_opencv_operations()
        else:
            print("用法:")
            print("  python benchmark.py --compare   # 对比不同实现")
            print("  python benchmark.py --opencv    # 分析OpenCV操作")
    else:
        print("选择测试模式:")
        print("1. 对比不同实现")
        print("2. 分析OpenCV操作")
        choice = input("请选择 (1/2): ")
        
        if choice == "1":
            compare_implementations()
        elif choice == "2":
            profile_opencv_operations()
        else:
            print("无效选择")
