# 性能分析工具使用指南

本项目提供了多种方法来查看和分析每一步操作的耗时，帮助你优化代码性能。

## 文件说明

- `main.py` - 原始代码，已添加基本的计时功能
- `main_profiled.py` - 使用高级性能分析器的版本
- `performance_profiler.py` - 高级性能分析工具
- `benchmark.py` - 性能基准测试脚本

## 使用方法

### 1. 基本计时（main.py）

运行修改后的主程序：
```bash
python main.py
```

输出示例：
```
图像读取: 15.23 ms
颜色范围掩码创建: 2.45 ms
形态学操作: 8.67 ms
轮廓检测: 12.34 ms
  轮廓 0 - 圆形检测: 0.15 ms
  轮廓 0 - ROI计算: 0.08 ms
  轮廓 0 - 图像裁剪: 1.23 ms
  轮廓 0 - 掩码创建: 2.45 ms
  轮廓 0 - 环形区域提取: 3.67 ms
  轮廓 0 - 图像保存: 45.23 ms
  轮廓 0 总耗时: 52.81 ms
结果图像保存: 23.45 ms

总耗时: 164.83 ms (0.165 秒)
```

### 2. 高级性能分析（main_profiled.py）

#### 基本分析
```bash
python main_profiled.py
```

#### 使用 cProfile 详细分析
```bash
python main_profiled.py --cprofile
```

这会显示：
- 每个函数的调用次数
- 每个函数的总耗时
- 每个函数的平均耗时
- 内存使用变化（如果安装了 psutil）
- cProfile 的详细函数调用分析

### 3. 性能基准测试（benchmark.py）

#### 对比不同实现
```bash
python benchmark.py --compare
```

#### 分析 OpenCV 操作
```bash
python benchmark.py --opencv
```

#### 交互式选择
```bash
python benchmark.py
```

## 性能分析功能

### 1. 时间测量
- **Wall Clock Time**: 实际经过的时间
- **CPU Time**: CPU 实际工作的时间
- **平均时间**: 多次运行的平均值
- **最小/最大时间**: 性能波动范围

### 2. 内存监控
如果安装了 `psutil`，可以监控内存使用：
```bash
pip install psutil
```

### 3. 函数级分析
使用装饰器分析特定函数：
```python
from performance_profiler import time_function

@time_function("我的函数")
def my_function():
    # 你的代码
    pass
```

### 4. 代码块分析
使用上下文管理器分析代码块：
```python
from performance_profiler import timer

with timer("图像处理"):
    # 你的图像处理代码
    pass
```

## 优化建议

根据性能分析结果，常见的优化方向：

### 1. I/O 操作优化
- 图像读取/保存通常是最耗时的操作
- 考虑使用更快的图像格式
- 批量处理多个图像

### 2. 算法优化
- 减少不必要的计算
- 使用更高效的 OpenCV 函数
- 预计算常量值

### 3. 内存优化
- 避免不必要的图像复制
- 重用数组和缓冲区
- 及时释放大型对象

### 4. 并行处理
- 对于多个轮廓的处理可以并行化
- 使用 multiprocessing 或 threading

## 示例：优化前后对比

```python
# 优化前
for cnt in contours:
    area = cv2.contourArea(cnt)
    if area < 50:
        continue
    (x, y), radius = cv2.minEnclosingCircle(cnt)
    circle_area = np.pi * (radius ** 2)
    if area / circle_area > 0.7:
        # 处理圆形轮廓
        pass

# 优化后
min_area = 50
min_circularity = 0.7
for cnt in contours:
    area = cv2.contourArea(cnt)
    if area < min_area:
        continue
    
    # 使用周长计算圆形度，更快
    perimeter = cv2.arcLength(cnt, True)
    if perimeter == 0:
        continue
    circularity = 4 * np.pi * area / (perimeter * perimeter)
    
    if circularity > min_circularity:
        # 处理圆形轮廓
        pass
```

## 常用命令总结

```bash
# 基本性能分析
python main.py

# 详细性能分析
python main_profiled.py

# cProfile 分析
python main_profiled.py --cprofile

# 性能对比测试
python benchmark.py --compare

# OpenCV 操作分析
python benchmark.py --opencv
```

## 注意事项

1. 第一次运行可能包含缓存加载时间，建议多次运行取平均值
2. 确保测试环境一致（CPU 负载、内存使用等）
3. 大文件 I/O 操作会受到磁盘性能影响
4. 某些 OpenCV 操作可能使用多线程，CPU 时间可能小于实际时间
